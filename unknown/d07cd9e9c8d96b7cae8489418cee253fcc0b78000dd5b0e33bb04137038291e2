-- Create RPC function to atomically create user and project
CREATE OR REPLACE FUNCTION create_project_with_user(
  p_user_id UUID,
  p_user_email TEXT,
  p_user_name TEXT,
  p_user_avatar TEXT,
  p_project_name TEXT,
  p_project_description TEXT,
  p_source_language TEXT,
  p_target_languages TEXT[],
  p_due_date TIMESTAMPTZ DEFAULT NULL,
  p_budget DECIMAL DEFAULT NULL,
  p_priority TEXT DEFAULT 'medium',
  p_organization_id UUID DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_project_id UUID;
  v_result JSON;
BEGIN
  -- Ensure user exists (upsert)
  INSERT INTO users (
    id,
    email,
    name,
    avatar_url,
    email_verified,
    created_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_user_email,
    p_user_name,
    p_user_avatar,
    NOW(),
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    name = EXCLUDED.name,
    avatar_url = EXCLUDED.avatar_url,
    updated_at = NOW();

  -- Generate project ID
  v_project_id := gen_random_uuid();

  -- Create project
  INSERT INTO projects (
    id,
    name,
    description,
    source_language,
    target_languages,
    due_date,
    budget,
    priority,
    status,
    created_by,
    organization_id,
    total_segments,
    completed_segments,
    reviewed_segments,
    spent,
    created_at,
    updated_at
  ) VALUES (
    v_project_id,
    p_project_name,
    p_project_description,
    p_source_language,
    p_target_languages,
    p_due_date,
    p_budget,
    p_priority,
    'draft',
    p_user_id,
    p_organization_id,
    0,
    0,
    0,
    0,
    NOW(),
    NOW()
  );

  -- Return the created project
  SELECT json_build_object(
    'id', id,
    'name', name,
    'description', description,
    'source_language', source_language,
    'target_languages', target_languages,
    'due_date', due_date,
    'budget', budget,
    'priority', priority,
    'status', status,
    'created_by', created_by,
    'organization_id', organization_id,
    'total_segments', total_segments,
    'completed_segments', completed_segments,
    'reviewed_segments', reviewed_segments,
    'spent', spent,
    'created_at', created_at,
    'updated_at', updated_at
  ) INTO v_result
  FROM projects
  WHERE id = v_project_id;

  RETURN v_result;
END;
$$;
