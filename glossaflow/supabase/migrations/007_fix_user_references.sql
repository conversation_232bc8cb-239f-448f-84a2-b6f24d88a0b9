-- Fix foreign key references to use users table consistently
-- This migration updates all auth.users references to use the users table instead

-- Drop existing foreign key constraints that reference auth.users (only if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') THEN
        ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_created_by_fkey;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_members') THEN
        ALTER TABLE project_members DROP CONSTRAINT IF EXISTS project_members_user_id_fkey;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_members') THEN
        ALTER TABLE team_members DROP CONSTRAINT IF EXISTS team_members_user_id_fkey;
        ALTER TABLE team_members DROP CONSTRAINT IF EXISTS team_members_created_by_fkey;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_invitations') THEN
        ALTER TABLE team_invitations DROP CONSTRAINT IF EXISTS team_invitations_invited_by_fkey;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
        ALTER TABLE credit_transactions DROP CONSTRAINT IF EXISTS credit_transactions_user_id_fkey;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_activity') THEN
        ALTER TABLE project_activity DROP CONSTRAINT IF EXISTS project_activity_user_id_fkey;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminology_entries') THEN
        ALTER TABLE terminology_entries DROP CONSTRAINT IF EXISTS terminology_entries_created_by_fkey;
        ALTER TABLE terminology_entries DROP CONSTRAINT IF EXISTS terminology_entries_reviewed_by_fkey;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminology_usage_log') THEN
        ALTER TABLE terminology_usage_log DROP CONSTRAINT IF EXISTS terminology_usage_log_used_by_fkey;
    END IF;
END $$;

-- Add new foreign key constraints that reference users table (only if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') THEN
        ALTER TABLE projects
        ADD CONSTRAINT projects_created_by_fkey
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_members') THEN
        ALTER TABLE project_members
        ADD CONSTRAINT project_members_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_members') THEN
        ALTER TABLE team_members
        ADD CONSTRAINT team_members_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

        ALTER TABLE team_members
        ADD CONSTRAINT team_members_created_by_fkey
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_invitations') THEN
        ALTER TABLE team_invitations
        ADD CONSTRAINT team_invitations_invited_by_fkey
        FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
        ALTER TABLE credit_transactions
        ADD CONSTRAINT credit_transactions_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_activity') THEN
        ALTER TABLE project_activity
        ADD CONSTRAINT project_activity_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Check if terminology_entries table exists and has the columns
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminology_entries') THEN
        -- Add foreign key constraints for terminology_entries
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'terminology_entries' AND column_name = 'created_by') THEN
            ALTER TABLE terminology_entries 
            ADD CONSTRAINT terminology_entries_created_by_fkey 
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;
        END IF;
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'terminology_entries' AND column_name = 'reviewed_by') THEN
            ALTER TABLE terminology_entries 
            ADD CONSTRAINT terminology_entries_reviewed_by_fkey 
            FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL;
        END IF;
    END IF;
END $$;

-- Check if terminology_usage_log table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminology_usage_log') THEN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'terminology_usage_log' AND column_name = 'used_by') THEN
            ALTER TABLE terminology_usage_log 
            ADD CONSTRAINT terminology_usage_log_used_by_fkey 
            FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL;
        END IF;
    END IF;
END $$;

-- Update RLS policies to use users table instead of auth.users
-- Note: auth.uid() still works as it returns the authenticated user's ID

-- Comments for documentation (only if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') THEN
        COMMENT ON CONSTRAINT projects_created_by_fkey ON projects IS 'References users table for NextAuth.js compatibility';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_members') THEN
        COMMENT ON CONSTRAINT project_members_user_id_fkey ON project_members IS 'References users table for NextAuth.js compatibility';
    END IF;
END $$;
