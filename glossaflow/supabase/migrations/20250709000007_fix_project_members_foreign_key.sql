-- Fix project_members foreign key constraint to reference public.users instead of auth.users
-- This fixes the issue where project creators cannot be added as members

-- Drop the existing foreign key constraint
ALTER TABLE project_members 
DROP CONSTRAINT IF EXISTS project_members_user_id_fkey;

-- Add the correct foreign key constraint that references public.users
ALTER TABLE project_members 
ADD CONSTRAINT project_members_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Add an index for better performance
CREATE INDEX IF NOT EXISTS idx_project_members_user_id ON project_members(user_id);
CREATE INDEX IF NOT EXISTS idx_project_members_project_id ON project_members(project_id);
