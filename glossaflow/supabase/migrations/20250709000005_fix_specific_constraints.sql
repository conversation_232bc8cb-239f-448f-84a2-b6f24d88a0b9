-- Fix specific foreign key constraints that are causing issues
-- This migration targets the exact constraints mentioned in the error messages

-- Drop the problematic constraints
ALTER TABLE project_members DROP CONSTRAINT IF EXISTS project_members_user_id_fkey;
ALTER TABLE project_activity DROP CONSTRAINT IF EXISTS project_activity_user_id_fkey;
ALTER TABLE team_members DROP CONSTRAINT IF EXISTS team_members_user_id_fkey;
ALTER TABLE organization_members DROP CONSTRAINT IF EXISTS organization_members_user_id_fkey;
ALTER TABLE organization_members DROP CONSTRAINT IF EXISTS organization_members_invited_by_fkey;

-- Ensure public.users table exists
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    avatar_url TEXT,
    email_verified TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    email_verified_at TIMESTAMPTZ,
    image TEXT
);

-- Add correct constraints pointing to public.users
ALTER TABLE project_members
ADD CONSTRAINT project_members_user_id_fkey
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE project_activity
ADD CONSTRAINT project_activity_user_id_fkey
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE team_members
ADD CONSTRAINT team_members_user_id_fkey
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Fix organization_members if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'organization_members') THEN
        ALTER TABLE organization_members
        ADD CONSTRAINT organization_members_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
        
        ALTER TABLE organization_members
        ADD CONSTRAINT organization_members_invited_by_fkey
        FOREIGN KEY (invited_by) REFERENCES public.users(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Verify the constraints were created
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    RAISE NOTICE 'Verifying new constraints:';
    FOR constraint_record IN
        SELECT 
            tc.table_name,
            tc.constraint_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND ccu.table_name = 'users'
            AND ccu.table_schema = 'public'
            AND tc.table_name IN ('project_members', 'project_activity', 'team_members', 'organization_members')
    LOOP
        RAISE NOTICE 'Table: %, Constraint: %, Column: % -> %.%', 
            constraint_record.table_name,
            constraint_record.constraint_name,
            constraint_record.column_name,
            constraint_record.foreign_table_schema,
            constraint_record.foreign_table_name;
    END LOOP;
END $$;
