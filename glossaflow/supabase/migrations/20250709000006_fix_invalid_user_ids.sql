-- Fix invalid user IDs that are not proper UUIDs
-- This migration removes any users with invalid UUID formats

-- First, let's see what invalid user IDs exist
DO $$
DECLARE
    invalid_user RECORD;
BEGIN
    -- Find users with invalid UUID format
    FOR invalid_user IN 
        SELECT id, email FROM users 
        WHERE id !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    LOOP
        RAISE NOTICE 'Found invalid user ID: % for email: %', invalid_user.id, invalid_user.email;
        
        -- Delete the user with invalid ID
        DELETE FROM users WHERE id = invalid_user.id;
        
        RAISE NOTICE 'Deleted user with invalid ID: %', invalid_user.id;
    END LOOP;
END $$;

-- Add a constraint to ensure all future user IDs are valid UUIDs
-- (This is already enforced by the UUID type, but let's be explicit)
ALTER TABLE users 
ADD CONSTRAINT users_id_is_uuid 
CHECK (id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$');
