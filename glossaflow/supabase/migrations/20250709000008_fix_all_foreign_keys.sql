-- Fix all foreign key constraints to reference public.users instead of auth.users
-- This resolves the schema relationship issues

-- 1. Fix project_members table foreign key
ALTER TABLE project_members 
DROP CONSTRAINT IF EXISTS project_members_user_id_fkey;

ALTER TABLE project_members 
ADD CONSTRAINT project_members_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 2. Fix project_activity table foreign key (if it exists)
ALTER TABLE project_activity 
DROP CONSTRAINT IF EXISTS project_activity_user_id_fkey;

ALTER TABLE project_activity 
ADD CONSTRAINT project_activity_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 3. Fix any other tables that might reference auth.users
-- Check organization_members table
ALTER TABLE organization_members 
DROP CONSTRAINT IF EXISTS organization_members_user_id_fkey;

ALTER TABLE organization_members 
ADD CONSTRAINT organization_members_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 4. Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_members_user_id ON project_members(user_id);
CREATE INDEX IF NOT EXISTS idx_project_members_project_id ON project_members(project_id);
CREATE INDEX IF NOT EXISTS idx_project_activity_user_id ON project_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_project_activity_project_id ON project_activity(project_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON organization_members(user_id);

-- 5. Refresh the schema cache
NOTIFY pgrst, 'reload schema';
