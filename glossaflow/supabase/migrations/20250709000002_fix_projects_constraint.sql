-- Diagnose and fix the projects foreign key constraint issue
-- This migration will drop the problematic constraint and recreate it correctly

-- First, let's see what constraints exist
DO $$
DECLARE
    constraint_info RECORD;
BEGIN
    -- Check current foreign key constraints on projects table
    FOR constraint_info IN
        SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = 'projects'
            AND kcu.column_name = 'created_by'
    <PERSON>OOP
        RAISE NOTICE 'Found constraint: % on %.% -> %.%', 
            constraint_info.constraint_name,
            constraint_info.table_name,
            constraint_info.column_name,
            constraint_info.foreign_table_name,
            constraint_info.foreign_column_name;
    END LOOP;
END $$;

-- Drop ALL foreign key constraints on projects.created_by
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Get all foreign key constraint names for projects.created_by
    FOR constraint_name IN
        SELECT tc.constraint_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = 'projects'
            AND kcu.column_name = 'created_by'
    LOOP
        EXECUTE format('ALTER TABLE projects DROP CONSTRAINT IF EXISTS %I', constraint_name);
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END LOOP;
END $$;

-- Ensure the users table exists and has the right structure
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    avatar_url TEXT,
    email_verified TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    email_verified_at TIMESTAMPTZ,
    image TEXT
);

-- Ensure the projects table has the created_by column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'created_by'
    ) THEN
        ALTER TABLE projects ADD COLUMN created_by UUID;
    END IF;
END $$;

-- Create the correct foreign key constraint
ALTER TABLE projects
ADD CONSTRAINT projects_created_by_fkey
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;

-- Verify the constraint was created correctly
DO $$
DECLARE
    constraint_info RECORD;
BEGIN
    RAISE NOTICE 'Verifying new constraint:';
    FOR constraint_info IN
        SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = 'projects'
            AND kcu.column_name = 'created_by'
    LOOP
        RAISE NOTICE 'New constraint: % on %.% -> %.%', 
            constraint_info.constraint_name,
            constraint_info.table_name,
            constraint_info.column_name,
            constraint_info.foreign_table_name,
            constraint_info.foreign_column_name;
    END LOOP;
END $$;

-- Test the constraint by checking if we can reference existing users
DO $$
DECLARE
    user_count INTEGER;
    user_record RECORD;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users;
    RAISE NOTICE 'Users table has % rows', user_count;

    IF user_count > 0 THEN
        RAISE NOTICE 'Sample users:';
        FOR user_record IN
            SELECT id FROM users LIMIT 3
        LOOP
            RAISE NOTICE 'User ID: %', user_record.id;
        END LOOP;
    END IF;
END $$;
