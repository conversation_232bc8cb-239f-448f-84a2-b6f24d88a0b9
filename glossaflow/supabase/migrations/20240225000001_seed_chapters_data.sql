-- Seed data for chapters and update translation segments with chapter references
-- This migration runs after the chapters table is created

-- First ensure the projects exist (they may have been dropped by later migrations)
INSERT INTO projects (id, organization_id, name, description, source_language, target_languages, document_type, priority, status, created_by) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        'Fantasy Novel Translation',
        'Translation of a popular fantasy novel series from English to Japanese',
        'en',
        ARRAY['ja'],
        'novel',
        'high',
        'in_progress',
        NULL
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000001',
        'Game UI Localization',
        'Localization of mobile game interface and story content',
        'en',
        ARRAY['ko', 'zh'],
        'game',
        'medium',
        'in_progress',
        NULL
    ),
    (
        '78f012a7-3ac3-46cc-a70c-d51e46353774',
        '00000000-0000-0000-0000-000000000001',
        'Epic Fantasy Series',
        'Multi-volume fantasy novel series translation project',
        'en',
        ARRAY['ja'],
        'novel',
        'high',
        'in_progress',
        NULL
    )
ON CONFLICT (id) DO NOTHING;

-- Insert demo chapters/series
INSERT INTO chapters (id, project_id, chapter_number, title, description, source_word_count, estimated_cost, status, progress_percentage, total_segments, completed_segments) VALUES
    (
        '78f012a7-3ac3-46cc-a70c-d51e46353774',
        '00000000-0000-0000-0000-000000000001',
        1,
        'The Dragon''s Awakening',
        'First chapter introducing the ancient dragon and the young hero',
        2500,
        25.00,
        'in_progress',
        65.5,
        45,
        30
    ),
    (
        'd2c92753-84c7-45cf-8e93-68e1571256bb',
        '00000000-0000-0000-0000-000000000001',
        2,
        'The Enchanted Forest',
        'The hero ventures into the mystical forest seeking ancient wisdom',
        3200,
        32.00,
        'pending',
        0.0,
        58,
        0
    ),
    (
        '00000000-0000-0000-0000-000000000010',
        '00000000-0000-0000-0000-000000000001',
        3,
        'The Crystal Cave',
        'Discovery of the magical crystal that holds the key to defeating the dragon',
        2800,
        28.00,
        'pending',
        0.0,
        52,
        0
    ),
    (
        '00000000-0000-0000-0000-000000000011',
        '00000000-0000-0000-0000-000000000002',
        1,
        'Main Menu Interface',
        'Core UI elements and navigation for the game main menu',
        150,
        5.00,
        'translated',
        100.0,
        25,
        25
    ),
    (
        '00000000-0000-0000-0000-000000000012',
        '00000000-0000-0000-0000-000000000002',
        2,
        'Character Creation',
        'UI strings and help text for character creation system',
        300,
        10.00,
        'reviewed',
        85.0,
        40,
        34
    )
ON CONFLICT (id) DO NOTHING;

-- Update translation segments to link to chapters
UPDATE translation_segments 
SET chapter_id = '78f012a7-3ac3-46cc-a70c-d51e46353774'
WHERE id IN ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002');

UPDATE translation_segments 
SET chapter_id = 'd2c92753-84c7-45cf-8e93-68e1571256bb'
WHERE id = '00000000-0000-0000-0000-000000000003';

UPDATE translation_segments 
SET chapter_id = '00000000-0000-0000-0000-000000000011'
WHERE id IN ('00000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000005');

-- Insert credits for the demo organization
INSERT INTO credits (organization_id, balance, total_purchased) VALUES
    ('00000000-0000-0000-0000-000000000001', 500.00, 500.00)
ON CONFLICT (organization_id) DO NOTHING;
