-- Final fix for projects foreign key constraint issue
-- This migration will ensure the projects table references the correct users table

-- First, let's see what constraints currently exist
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    RAISE NOTICE 'Current foreign key constraints on projects table:';
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = 'projects'
            AND kcu.column_name = 'created_by'
    LOOP
        RAISE NOTICE 'Constraint: % on %.% -> %.%.%', 
            constraint_record.constraint_name,
            constraint_record.table_name,
            constraint_record.column_name,
            constraint_record.foreign_table_schema,
            constraint_record.foreign_table_name,
            constraint_record.foreign_column_name;
    END LOOP;
END $$;

-- Drop ALL foreign key constraints on projects.created_by
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Get all foreign key constraint names for projects.created_by
    FOR constraint_name IN
        SELECT tc.constraint_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = 'projects'
            AND kcu.column_name = 'created_by'
    LOOP
        EXECUTE format('ALTER TABLE projects DROP CONSTRAINT IF EXISTS %I', constraint_name);
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END LOOP;
END $$;

-- Ensure the public.users table exists and has the right structure
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    avatar_url TEXT,
    email_verified TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    email_verified_at TIMESTAMPTZ,
    image TEXT
);

-- Ensure the projects table has the created_by column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'created_by'
    ) THEN
        ALTER TABLE projects ADD COLUMN created_by UUID;
    END IF;
END $$;

-- Create the correct foreign key constraint pointing to public.users
ALTER TABLE projects
ADD CONSTRAINT projects_created_by_fkey
FOREIGN KEY (created_by) REFERENCES public.users(id) ON DELETE SET NULL;

-- Verify the constraint was created correctly
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    RAISE NOTICE 'New foreign key constraints on projects table:';
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = 'projects'
            AND kcu.column_name = 'created_by'
    LOOP
        RAISE NOTICE 'New constraint: % on %.% -> %.%.%', 
            constraint_record.constraint_name,
            constraint_record.table_name,
            constraint_record.column_name,
            constraint_record.foreign_table_schema,
            constraint_record.foreign_table_name,
            constraint_record.foreign_column_name;
    END LOOP;
END $$;

-- Test the constraint by checking if we can reference existing users
DO $$
DECLARE
    user_count INTEGER;
    auth_user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM public.users;
    RAISE NOTICE 'public.users table has % rows', user_count;
    
    -- Also check auth.users for comparison
    BEGIN
        SELECT COUNT(*) INTO auth_user_count FROM auth.users;
        RAISE NOTICE 'auth.users table has % rows', auth_user_count;
    EXCEPTION
        WHEN others THEN
            RAISE NOTICE 'Could not access auth.users table';
    END;
END $$;
