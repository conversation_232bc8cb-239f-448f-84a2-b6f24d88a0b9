'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { ArrowLeft, BookOpen, Loader2, CheckCircle, AlertTriangle, Search, FileText } from 'lucide-react';
import { useCreateSeriesMutation, useGetSeriesQuery } from '@/lib/api/series';
import { useGetTerminologyForProjectQuery } from '@/lib/api/terminology';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

export default function NewSeriesPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = params.id as string;

  const [formData, setFormData] = useState({
    chapter_number: '',
    title: '',
    description: '',
  });

  const [createSeries, { isLoading }] = useCreateSeriesMutation();
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [terminologyOpen, setTerminologyOpen] = useState(false);
  const [terminologySearch, setTerminologySearch] = useState('');

  // Fetch existing series to determine next series number
  const { data: existingSeries } = useGetSeriesQuery({
    projectId,
    limit: 1000 // Get all series to find the highest number
  });

  // Fetch project terminology
  const { data: terminologyData, isLoading: terminologyLoading } = useGetTerminologyForProjectQuery({
    projectId
  });

  // Auto-increment series number when existing series data is loaded
  useEffect(() => {
    if (existingSeries?.data?.items && formData.chapter_number === '') {
      const existingNumbers = existingSeries.data.items.map(series => series.chapter_number);
      const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
      setFormData(prev => ({
        ...prev,
        chapter_number: nextNumber.toString()
      }));
    }
  }, [existingSeries, formData.chapter_number]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDescriptionChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      description: content
    }));
  };

  // Filter terminology based on search and approval status
  const filteredTerminology = terminologyData?.data?.filter(term => {
    // Only show approved terminology
    if (term.approvalStatus !== 'approved') return false;

    // Apply search filter
    if (!terminologySearch) return true;
    const searchLower = terminologySearch.toLowerCase();
    return (
      term.sourceTerm.toLowerCase().includes(searchLower) ||
      term.targetTerm.toLowerCase().includes(searchLower) ||
      term.category.toLowerCase().includes(searchLower)
    );
  }) || [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.chapter_number || !formData.title) {
      setMessage({
        type: 'error',
        text: 'Series number and title are required.'
      });
      return;
    }

    try {
      const result = await createSeries({
        project_id: projectId,
        chapter_number: parseInt(formData.chapter_number),
        title: formData.title,
        description: formData.description || undefined,
        source_word_count: 0, // Will be calculated automatically from uploaded content
      }).unwrap();

      setMessage({
        type: 'success',
        text: 'Series created successfully!'
      });

      // Navigate back to project details after a short delay
      setTimeout(() => {
        router.push(`/dashboard/projects/${projectId}`);
      }, 1500);
    } catch (error: any) {
      console.error('Error creating series:', error);
      setMessage({
        type: 'error',
        text: error?.data?.error || 'Failed to create series. Please try again.'
      });
    }
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push(`/dashboard/projects/${projectId}`)}
          className="self-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Project
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Create New Series</h1>
          <p className="text-gray-600 mt-1">Add a new series to your translation project</p>
        </div>
      </div>

      {/* Messages */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          {message.type === 'error' ? (
            <AlertTriangle className="h-4 w-4 text-red-600" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-600" />
          )}
          <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center text-lg sm:text-xl">
            <BookOpen className="mr-2 h-5 w-5" />
            Series Details
          </CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Enter the details for your new series. The series number should be unique within this project.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="chapter_number" className="text-sm font-medium">
                Series Number *
              </Label>
              <Input
                id="chapter_number"
                name="chapter_number"
                type="number"
                min="1"
                value={formData.chapter_number}
                onChange={handleInputChange}
                placeholder="Auto-filled based on existing series"
                required
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                Automatically suggested based on existing series. You can modify if needed.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium">
                Series Title *
              </Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter series title"
                required
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Description
              </Label>
              <RichTextEditor
                content={formData.description}
                onChange={handleDescriptionChange}
                placeholder="Enter a detailed description of this series. You can use formatting to organize your content."
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                Use the toolbar above to format your description with headings, lists, and emphasis.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/dashboard/projects/${projectId}`)}
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <BookOpen className="mr-2 h-4 w-4" />
                    Create Series
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
        </div>

        {/* Terminology Reference Panel - Takes 1 column on large screens */}
        <div className="lg:col-span-1">
          <Card className="w-full h-fit sticky top-6">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <FileText className="mr-2 h-5 w-5" />
                Project Terminology
              </CardTitle>
              <CardDescription className="text-sm">
                Reference approved terminology while writing your series description
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search terminology..."
                  value={terminologySearch}
                  onChange={(e) => setTerminologySearch(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Terminology List */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {terminologyLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                    <span className="ml-2 text-sm text-gray-500">Loading terminology...</span>
                  </div>
                ) : filteredTerminology.length > 0 ? (
                  filteredTerminology.map((term) => (
                    <div
                      key={term.id}
                      className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex flex-col space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-sm text-gray-900">
                            {term.sourceTerm}
                          </span>
                          <Badge variant="secondary" className="text-xs">
                            {term.category}
                          </Badge>
                        </div>
                        <span className="text-sm text-gray-600">
                          → {term.targetTerm}
                        </span>
                        {term.notes && (
                          <p className="text-xs text-gray-500 mt-1">
                            {term.notes}
                          </p>
                        )}
                      </div>
                    </div>
                  ))
                ) : terminologyData?.data?.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-sm text-gray-500">
                      No approved terminology found for this project.
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Add terminology entries to help maintain consistency.
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Search className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-sm text-gray-500">
                      No terminology matches your search.
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Try different keywords or clear the search.
                    </p>
                  </div>
                )}
              </div>

              {/* Quick Stats */}
              {terminologyData?.data && terminologyData.data.length > 0 && (
                <div className="pt-3 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    {filteredTerminology.length} of {terminologyData.data.length} terms
                    {terminologySearch && ` matching "${terminologySearch}"`}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
