import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function POST(request: NextRequest) {
  try {
    // Get all users
    const { data: allUsers, error: fetchError } = await supabase
      .from('users')
      .select('id, email');

    if (fetchError) {
      console.error('Error fetching users:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch users', details: fetchError },
        { status: 500 }
      );
    }

    const invalidUsers = [];
    const validUsers = [];

    // Check each user ID format
    for (const user of allUsers || []) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(user.id)) {
        invalidUsers.push(user);
      } else {
        validUsers.push(user);
      }
    }

    console.log('Found invalid users:', invalidUsers);
    console.log('Found valid users:', validUsers);

    // Delete invalid users
    if (invalidUsers.length > 0) {
      const invalidIds = invalidUsers.map(u => u.id);
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .in('id', invalidIds);

      if (deleteError) {
        console.error('Error deleting invalid users:', deleteError);
        return NextResponse.json(
          { error: 'Failed to delete invalid users', details: deleteError },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      message: 'User cleanup completed',
      invalidUsersDeleted: invalidUsers.length,
      validUsersRemaining: validUsers.length,
      invalidUsers,
      validUsers,
      success: true,
    });

  } catch (error) {
    console.error('Cleanup users error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
