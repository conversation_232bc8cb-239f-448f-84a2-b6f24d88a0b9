import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Check foreign key constraints on projects table
    const { data: constraints, error: constraintsError } = await supabase
      .rpc('get_table_constraints', { table_name: 'projects' })
      .single();

    // If the RPC doesn't exist, use a direct SQL query
    const { data: constraintsQuery, error: constraintsQueryError } = await supabase
      .from('information_schema.table_constraints')
      .select('*')
      .eq('table_name', 'projects')
      .eq('constraint_type', 'FOREIGN KEY');

    // Get detailed constraint information
    const { data: constraintDetails, error: constraintDetailsError } = await supabase
      .from('information_schema.key_column_usage')
      .select('*')
      .eq('table_name', 'projects')
      .eq('column_name', 'created_by');

    // Get referential constraints
    const { data: refConstraints, error: refConstraintsError } = await supabase
      .from('information_schema.referential_constraints')
      .select('*');

    return NextResponse.json({
      constraints: {
        data: constraints,
        error: constraintsError,
      },
      constraintsQuery: {
        data: constraintsQuery,
        error: constraintsQueryError,
      },
      constraintDetails: {
        data: constraintDetails,
        error: constraintDetailsError,
      },
      refConstraints: {
        data: refConstraints,
        error: refConstraintsError,
      },
      success: true,
    });
  } catch (error) {
    console.error('Debug constraints error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
