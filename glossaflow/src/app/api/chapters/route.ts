import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';
import type { CreateChapterRequest } from '@/types';

// GET /api/chapters - Get chapters with filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const supabase = createServiceClient();

    // With Supabase adapter, session.user.id is the users table ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'chapter_number';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // If projectId is provided, verify user has access to that project
    if (projectId) {
      // Check if user is creator (primary access method for now)
      const { data: projectOwnership } = await supabase
        .from('projects')
        .select('id, created_by')
        .eq('id', projectId)
        .eq('created_by', userId)
        .single();

      if (!projectOwnership) {
        return NextResponse.json(
          { error: 'Project not found or access denied', success: false },
          { status: 404 }
        );
      }
    }

    // Build query - simplified to avoid complex joins
    let query = supabase
      .from('chapters')
      .select('*');

    // Apply filters
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    
    if (status) {
      query = query.eq('status', status);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: chapters, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch chapters', success: false },
        { status: 500 }
      );
    }

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        items: chapters || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
        },
      },
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

// POST /api/chapters - Create a new chapter
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const supabase = createServiceClient();

    const body: CreateChapterRequest = await request.json();
    const {
      project_id,
      chapter_number,
      title,
      description,
      source_word_count = 0,
      estimated_cost = 0,
    } = body;

    // Validate required fields
    if (!project_id || !chapter_number || !title) {
      return NextResponse.json(
        { error: 'Missing required fields: project_id, chapter_number, title', success: false },
        { status: 400 }
      );
    }

    // Check if user has access to the project (creator check)
    const { data: projectOwnership } = await supabase
      .from('projects')
      .select('id, created_by')
      .eq('id', project_id)
      .eq('created_by', session.user.id)
      .single();

    if (!projectOwnership) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Check if chapter number already exists for this project
    const { data: existingChapter } = await supabase
      .from('chapters')
      .select('id')
      .eq('project_id', project_id)
      .eq('chapter_number', chapter_number)
      .single();

    if (existingChapter) {
      return NextResponse.json(
        { error: 'Chapter number already exists for this project', success: false },
        { status: 409 }
      );
    }

    // Create the chapter
    const { data: chapter, error: createError } = await supabase
      .from('chapters')
      .insert({
        project_id,
        chapter_number,
        title,
        description,
        source_word_count,
        estimated_cost,
        status: 'pending',
        progress_percentage: 0,
      })
      .select()
      .single();

    if (createError) {
      console.error('Chapter creation error:', createError);
      return NextResponse.json(
        { error: 'Failed to create chapter', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: chapter,
      message: 'Chapter created successfully',
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
