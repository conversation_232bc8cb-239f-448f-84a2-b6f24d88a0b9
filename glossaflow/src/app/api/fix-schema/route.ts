import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function POST(request: NextRequest) {
  try {
    console.log('Starting schema analysis...');

    // Let's first check what constraints currently exist
    const { data: constraints, error: constraintsError } = await supabase
      .from('information_schema.table_constraints')
      .select('table_name, constraint_name, constraint_type')
      .eq('constraint_type', 'FOREIGN KEY')
      .in('table_name', ['project_members', 'project_activity', 'organization_members']);

    console.log('Current constraints:', constraints);

    // Check what foreign key relationships exist
    const { data: foreignKeys, error: foreignKeysError } = await supabase
      .from('information_schema.key_column_usage')
      .select('table_name, column_name, referenced_table_name, referenced_column_name')
      .in('table_name', ['project_members', 'project_activity', 'organization_members']);

    console.log('Current foreign keys:', foreignKeys);

    // Try to query project_members to see current state
    const { data: projectMembers, error: projectMembersError } = await supabase
      .from('project_members')
      .select('*')
      .limit(1);

    console.log('Project members query:', { projectMembers, projectMembersError });

    // Try to query project_activity to see current state
    const { data: projectActivity, error: projectActivityError } = await supabase
      .from('project_activity')
      .select('*')
      .limit(1);

    console.log('Project activity query:', { projectActivity, projectActivityError });

    return NextResponse.json({
      message: 'Schema analysis completed',
      constraints: {
        data: constraints,
        error: constraintsError,
      },
      foreignKeys: {
        data: foreignKeys,
        error: foreignKeysError,
      },
      projectMembers: {
        data: projectMembers,
        error: projectMembersError,
      },
      projectActivity: {
        data: projectActivity,
        error: projectActivityError,
      },
      note: 'Use Supabase dashboard to run the migration SQL manually',
      migrationSQL: `
-- Run this in Supabase SQL Editor:
ALTER TABLE project_members DROP CONSTRAINT IF EXISTS project_members_user_id_fkey;
ALTER TABLE project_members ADD CONSTRAINT project_members_user_id_fkey
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE project_activity DROP CONSTRAINT IF EXISTS project_activity_user_id_fkey;
ALTER TABLE project_activity ADD CONSTRAINT project_activity_user_id_fkey
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
      `,
      success: true,
    });

  } catch (error) {
    console.error('Schema analysis error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
