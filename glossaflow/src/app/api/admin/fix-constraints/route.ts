import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    console.log('Fixing all database constraints...');

    // Step 1: Drop all foreign key constraints that reference auth.users
    console.log('Step 1: Dropping constraints that reference auth.users...');
    
    const dropConstraintsQuery = `
      DO $$
      DECLARE
          constraint_record RECORD;
      BEGIN
          FOR constraint_record IN
              SELECT 
                  tc.table_name,
                  tc.constraint_name
              FROM information_schema.table_constraints AS tc
              JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
              JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
              WHERE tc.constraint_type = 'FOREIGN KEY'
                  AND ccu.table_name = 'users'
                  AND ccu.table_schema = 'auth'
          LOOP
              EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I', 
                  constraint_record.table_name, 
                  constraint_record.constraint_name);
          END LOOP;
      END $$;
    `;

    // For now, let's skip the complex SQL and focus on the specific constraints we know need fixing
    console.log('Skipping complex constraint dropping for now...');

    // Step 2: Ensure public.users table exists (we know it does from previous work)
    console.log('Step 2: Users table already exists from previous fixes...');

    // Step 3: Try to fix the specific constraints that are causing issues
    console.log('Step 3: Fixing specific foreign key constraints...');

    const results = [];

    // Try to drop and recreate the problematic constraints one by one
    const constraintFixes = [
      {
        name: 'project_members_user_id_fkey',
        dropSql: 'ALTER TABLE project_members DROP CONSTRAINT IF EXISTS project_members_user_id_fkey',
        addSql: 'ALTER TABLE project_members ADD CONSTRAINT project_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE'
      },
      {
        name: 'project_activity_user_id_fkey',
        dropSql: 'ALTER TABLE project_activity DROP CONSTRAINT IF EXISTS project_activity_user_id_fkey',
        addSql: 'ALTER TABLE project_activity ADD CONSTRAINT project_activity_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL'
      }
    ];

    for (const fix of constraintFixes) {
      try {
        console.log(`Fixing constraint: ${fix.name}`);

        // Try to drop the constraint first
        const { error: dropError } = await supabase.rpc('sql', { query: fix.dropSql });
        console.log(`Drop result for ${fix.name}:`, dropError ? dropError.message : 'Success');

        // Try to add the new constraint
        const { error: addError } = await supabase.rpc('sql', { query: fix.addSql });
        console.log(`Add result for ${fix.name}:`, addError ? addError.message : 'Success');

        results.push({
          constraint: fix.name,
          dropSuccess: !dropError,
          addSuccess: !addError,
          dropError: dropError?.message || null,
          addError: addError?.message || null
        });

      } catch (error) {
        console.error(`Error fixing constraint ${fix.name}:`, error);
        results.push({
          constraint: fix.name,
          dropSuccess: false,
          addSuccess: false,
          error: error.message
        });
      }
    }

    return NextResponse.json({
      message: 'Database constraints fix completed',
      results,
      success: true,
    });
  } catch (error) {
    console.error('Fix constraints error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error, success: false },
      { status: 500 }
    );
  }
}
