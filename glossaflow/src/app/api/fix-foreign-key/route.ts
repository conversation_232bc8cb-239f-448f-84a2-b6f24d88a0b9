import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function POST(request: NextRequest) {
  try {
    console.log('Starting foreign key fix...');

    // For now, let's just check the current constraint and return info
    // We'll need to run the SQL manually in the Supabase dashboard

    // Check current project_members table structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.table_constraints')
      .select('*')
      .eq('table_name', 'project_members')
      .eq('constraint_type', 'FOREIGN KEY');

    console.log('Current constraints:', tableInfo);

    // Check if we can query project_members
    const { data: members, error: membersError } = await supabase
      .from('project_members')
      .select('*')
      .limit(5);

    console.log('Project members query result:', { members, membersError });

    return NextResponse.json({
      message: 'Foreign key analysis completed',
      tableInfo,
      tableError,
      members,
      membersError,
      note: 'Run the migration SQL manually in Supabase dashboard',
      sql: `
        ALTER TABLE project_members DROP CONSTRAINT IF EXISTS project_members_user_id_fkey;
        ALTER TABLE project_members ADD CONSTRAINT project_members_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
      `,
      success: true,
    });

  } catch (error) {
    console.error('Fix foreign key error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
