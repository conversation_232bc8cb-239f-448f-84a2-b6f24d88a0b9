import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log('Debug projects lookup:', {
      userId,
      userEmail,
    });

    // Get all projects (without filtering)
    const { data: allProjects, error: allProjectsError } = await supabase
      .from('projects')
      .select('id, name, created_by, status')
      .limit(10);

    // Get all project members
    const { data: allMembers, error: allMembersError } = await supabase
      .from('project_members')
      .select('project_id, user_id, role')
      .limit(20);

    // Get project members for current user
    const { data: userMembers, error: userMembersError } = await supabase
      .from('project_members')
      .select('project_id, role')
      .eq('user_id', userId);

    // Get projects where user is a member (using the fixed query)
    const { data: userProjects, error: userProjectsError } = await supabase
      .from('projects')
      .select(`
        *,
        project_members!inner(user_id, role)
      `)
      .eq('project_members.user_id', userId);

    return NextResponse.json({
      session: {
        userId,
        userEmail,
        userName: session.user.name,
      },
      allProjects: {
        data: allProjects,
        error: allProjectsError,
      },
      allMembers: {
        data: allMembers,
        error: allMembersError,
      },
      userMembers: {
        data: userMembers,
        error: userMembersError,
      },
      userProjects: {
        data: userProjects,
        error: userProjectsError,
      },
      success: true,
    });

  } catch (error) {
    console.error('Debug projects error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
