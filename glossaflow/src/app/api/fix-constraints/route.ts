import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    console.log('Fixing database constraints...');

    // Step 1: Check current constraints
    const { data: currentConstraints, error: constraintError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
          FROM information_schema.table_constraints AS tc
          JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
          WHERE tc.constraint_type = 'FOREIGN KEY'
              AND tc.table_name = 'projects'
              AND kcu.column_name = 'created_by';
        `
      });

    console.log('Current constraints:', currentConstraints);

    // Step 2: Drop existing constraints
    const dropConstraints = await supabase
      .rpc('sql', {
        query: `
          DO $$
          DECLARE
              constraint_name TEXT;
          BEGIN
              FOR constraint_name IN
                  SELECT tc.constraint_name
                  FROM information_schema.table_constraints AS tc
                  JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                      AND tc.table_schema = kcu.table_schema
                  WHERE tc.constraint_type = 'FOREIGN KEY'
                      AND tc.table_name = 'projects'
                      AND kcu.column_name = 'created_by'
              LOOP
                  EXECUTE format('ALTER TABLE projects DROP CONSTRAINT IF EXISTS %I', constraint_name);
              END LOOP;
          END $$;
        `
      });

    console.log('Drop constraints result:', dropConstraints);

    // Step 3: Ensure users table exists
    const createUsersTable = await supabase
      .rpc('sql', {
        query: `
          CREATE TABLE IF NOT EXISTS public.users (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              email TEXT UNIQUE NOT NULL,
              name TEXT,
              avatar_url TEXT,
              email_verified TIMESTAMPTZ,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              updated_at TIMESTAMPTZ DEFAULT NOW(),
              email_verified_at TIMESTAMPTZ,
              image TEXT
          );
        `
      });

    console.log('Create users table result:', createUsersTable);

    // Step 4: Add correct constraint
    const addConstraint = await supabase
      .rpc('sql', {
        query: `
          ALTER TABLE projects
          ADD CONSTRAINT projects_created_by_fkey
          FOREIGN KEY (created_by) REFERENCES public.users(id) ON DELETE SET NULL;
        `
      });

    console.log('Add constraint result:', addConstraint);

    // Step 5: Verify new constraints
    const { data: newConstraints, error: newConstraintError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
          FROM information_schema.table_constraints AS tc
          JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
          WHERE tc.constraint_type = 'FOREIGN KEY'
              AND tc.table_name = 'projects'
              AND kcu.column_name = 'created_by';
        `
      });

    console.log('New constraints:', newConstraints);

    return NextResponse.json({
      message: 'Database constraints fixed successfully',
      currentConstraints,
      newConstraints,
      success: true,
    });
  } catch (error) {
    console.error('Fix constraints error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error, success: false },
      { status: 500 }
    );
  }
}
