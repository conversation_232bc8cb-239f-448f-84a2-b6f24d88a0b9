import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log('Adding user to projects:', {
      userId,
      userEmail,
    });

    // Get all projects
    const { data: allProjects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name');

    if (projectsError) {
      console.error('Error fetching projects:', projectsError);
      return NextResponse.json(
        { error: 'Failed to fetch projects', details: projectsError },
        { status: 500 }
      );
    }

    // Add user as a member to all projects
    const memberships = [];
    for (const project of allProjects || []) {
      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('project_members')
        .select('id')
        .eq('project_id', project.id)
        .eq('user_id', userId)
        .single();

      if (!existingMember) {
        // Add user as project member
        const { data: newMember, error: memberError } = await supabase
          .from('project_members')
          .insert({
            project_id: project.id,
            user_id: userId,
            role: 'translator',
            languages: ['English'],
          })
          .select()
          .single();

        if (memberError) {
          console.error('Error adding member:', memberError);
        } else {
          memberships.push({
            project_id: project.id,
            project_name: project.name,
            member_id: newMember.id,
          });
        }
      } else {
        memberships.push({
          project_id: project.id,
          project_name: project.name,
          already_member: true,
        });
      }
    }

    return NextResponse.json({
      message: 'User added to projects',
      userId,
      userEmail,
      projects: allProjects,
      memberships,
      success: true,
    });

  } catch (error) {
    console.error('Add user to projects error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
