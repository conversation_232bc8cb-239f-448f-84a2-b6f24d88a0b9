import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log('Checking auth users:', { userId, userEmail });

    // Check auth.users table
    const { data: authUsers, error: authUsersError } = await supabase
      .from('auth.users')
      .select('id, email')
      .eq('email', userEmail);

    // Check public.users table
    const { data: publicUsers, error: publicUsersError } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', userEmail);

    // Try to query project_members to see what happens
    const { data: projectMembers, error: projectMembersError } = await supabase
      .from('project_members')
      .select('*')
      .limit(5);

    return NextResponse.json({
      session: {
        userId,
        userEmail,
      },
      authUsers: {
        data: authUsers,
        error: authUsersError,
      },
      publicUsers: {
        data: publicUsers,
        error: publicUsersError,
      },
      projectMembers: {
        data: projectMembers,
        error: projectMembersError,
      },
      success: true,
    });

  } catch (error) {
    console.error('Check auth users error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
