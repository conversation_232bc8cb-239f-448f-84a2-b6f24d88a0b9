import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const baseQuery = fetchBaseQuery({
  baseUrl: '/api',
  prepareHeaders: (headers) => {
    // NextAuth v4 doesn't use accessToken by default with JWT strategy
    // The session cookie is automatically sent by the browser
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery,
  tagTypes: [
    'User',
    'Project',
    'Terminology',
    'TermCandidate',
    'Translation',
    'Comment',
    'File',
    'Team',
    'TeamMember',
    'TeamRole',
    'TeamInvitation',
    'Organization',
    'Chapter',
    'Credits',
  ],
  endpoints: () => ({}),
});

export type ApiResponse<T> = {
  data: T;
  message?: string;
  success: boolean;
};

export type PaginatedResponse<T> = ApiResponse<{
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}>;

export type ApiError = {
  error: string;
  message?: string;
  statusCode?: number;
};
