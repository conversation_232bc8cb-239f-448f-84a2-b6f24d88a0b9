const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://awhtjodmtstnsobmagfc.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF3aHRqb2RtdHN0bnNvYm1hZ2ZjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjAyNTAzMCwiZXhwIjoyMDY3NjAxMDMwfQ.MwbzELJQjJvCbUczrJNEOhVMKufHhRvc0GiQwGJvgko';

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugChapters() {
  const projectId = '0e6c4232-21b8-4a3c-bca3-5204bc022446';
  const seriesId = '240eb12c-9e67-4aec-9106-e5549968c9dc';
  
  console.log('=== Debugging Chapters ===');
  console.log('Project ID:', projectId);
  console.log('Series ID:', seriesId);
  console.log('');
  
  // Check if project exists
  console.log('1. Checking if project exists...');
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('*')
    .eq('id', projectId)
    .single();
    
  if (projectError) {
    console.log('Project error:', projectError);
  } else {
    console.log('Project found:', project?.name || 'No name');
  }
  console.log('');
  
  // Check all chapters for this project
  console.log('2. Checking all chapters for this project...');
  const { data: chapters, error: chaptersError } = await supabase
    .from('chapters')
    .select('*')
    .eq('project_id', projectId);
    
  if (chaptersError) {
    console.log('Chapters error:', chaptersError);
  } else {
    console.log('Chapters found:', chapters?.length || 0);
    chapters?.forEach(chapter => {
      console.log(`  - ID: ${chapter.id}, Number: ${chapter.chapter_number}, Title: ${chapter.title}`);
    });
  }
  console.log('');
  
  // Check if the specific series/chapter exists
  console.log('3. Checking if specific series exists...');
  const { data: specificChapter, error: specificError } = await supabase
    .from('chapters')
    .select('*')
    .eq('id', seriesId)
    .single();
    
  if (specificError) {
    console.log('Specific chapter error:', specificError);
  } else {
    console.log('Specific chapter found:', specificChapter?.title || 'No title');
  }
  console.log('');
  
  // Check all chapters in database (to see if ID exists anywhere)
  console.log('4. Checking if series ID exists anywhere in database...');
  const { data: allChapters, error: allError } = await supabase
    .from('chapters')
    .select('id, project_id, title')
    .eq('id', seriesId);
    
  if (allError) {
    console.log('All chapters error:', allError);
  } else {
    console.log('Found chapters with this ID:', allChapters?.length || 0);
    allChapters?.forEach(chapter => {
      console.log(`  - Project: ${chapter.project_id}, Title: ${chapter.title}`);
    });
  }
}

debugChapters().catch(console.error);
