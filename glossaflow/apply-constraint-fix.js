const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function applyConstraintFix() {
  try {
    console.log('Applying constraint fixes...');
    
    // Read the migration file
    const migrationSQL = fs.readFileSync('./supabase/migrations/20250709000005_fix_specific_constraints.sql', 'utf8');
    
    // Split into individual statements (simple approach)
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        console.log(`\nExecuting statement ${i + 1}:`);
        console.log(statement.substring(0, 100) + '...');
        
        try {
          // For simple ALTER TABLE statements, we can try direct execution
          if (statement.includes('ALTER TABLE') || statement.includes('CREATE TABLE')) {
            // This is a simplified approach - in production you'd want better SQL parsing
            console.log('Skipping complex statement for now...');
          }
        } catch (error) {
          console.error(`Error executing statement ${i + 1}:`, error.message);
        }
      }
    }
    
    // Instead, let's try the individual constraint fixes manually
    console.log('\nApplying individual constraint fixes...');
    
    const fixes = [
      {
        name: 'Drop project_members constraint',
        sql: 'ALTER TABLE project_members DROP CONSTRAINT IF EXISTS project_members_user_id_fkey'
      },
      {
        name: 'Add project_members constraint',
        sql: 'ALTER TABLE project_members ADD CONSTRAINT project_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE'
      },
      {
        name: 'Drop project_activity constraint',
        sql: 'ALTER TABLE project_activity DROP CONSTRAINT IF EXISTS project_activity_user_id_fkey'
      },
      {
        name: 'Add project_activity constraint',
        sql: 'ALTER TABLE project_activity ADD CONSTRAINT project_activity_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL'
      }
    ];
    
    for (const fix of fixes) {
      console.log(`\nApplying: ${fix.name}`);
      try {
        // We'll need to use a different approach since direct SQL execution might not work
        console.log('SQL:', fix.sql);
        console.log('(Manual execution required)');
      } catch (error) {
        console.error(`Error with ${fix.name}:`, error.message);
      }
    }
    
    console.log('\nConstraint fix script completed. Manual SQL execution may be required.');
    
  } catch (error) {
    console.error('Script error:', error);
  }
}

applyConstraintFix();
