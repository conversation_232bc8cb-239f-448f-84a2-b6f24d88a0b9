-- Fix foreign key constraint for projects.created_by
-- This script ensures the constraint references users(id) instead of auth.users(id)

-- First, check if the constraint exists and drop it
DO $$
BEGIN
    -- Drop the constraint if it exists (regardless of which table it references)
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'projects' 
        AND constraint_name = 'projects_created_by_fkey'
    ) THEN
        ALTER TABLE projects DROP CONSTRAINT projects_created_by_fkey;
        RAISE NOTICE 'Dropped existing projects_created_by_fkey constraint';
    END IF;
END $$;

-- Add the correct foreign key constraint
ALTER TABLE projects
ADD CONSTRAINT projects_created_by_fkey
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;

-- Verify the constraint was created
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = 'projects'
    AND kcu.column_name = 'created_by';
